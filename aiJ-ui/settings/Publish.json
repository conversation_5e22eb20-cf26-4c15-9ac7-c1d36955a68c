{"path": "D:\\leo7\\Program\\aij\\aiJ-client\\assets\\resources", "branchPath": "", "fileExtension": "bin", "packageCount": 2, "compressDesc": true, "binaryFormat": true, "jpegQuality": 80, "compressPNG": false, "codeGeneration": {"allowGenCode": true, "codePath": "/Users/<USER>/AiJProjects/aiJ/aiJ-client/assets/Script", "classNamePrefix": "UI_", "memberNamePrefix": "m_", "packageName": "", "ignoreNoname": false, "getMemberByName": true, "codeType": ""}, "includeHighResolution": 0, "branchProcessing": 0, "seperatedAtlasForBranch": false, "atlasSetting": {"maxSize": 2048, "paging": true, "sizeOption": "pot", "forceSquare": false, "allowRotation": false, "trimImage": false}, "include2x": false, "include3x": false, "include4x": false, "fileName": "Publish"}